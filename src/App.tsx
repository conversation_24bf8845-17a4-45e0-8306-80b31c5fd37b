import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import type { IwencaiDirectResponse, IwencaiCookieResponse, IwencaiApiResponse } from './types/electron'

function App() {
  const [count, setCount] = useState(0)
  const [directData, setDirectData] = useState<IwencaiDirectResponse | null>(null)
  const [cookieData, setCookieData] = useState<IwencaiCookieResponse | null>(null)
  const [apiData, setApiData] = useState<IwencaiApiResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [executeResult, setExecuteResult] = useState<any>(null)

  // 直接请求问财数据的函数
  const fetchIwencaiDirect = async () => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await window.electronAPI.fetchIwencaiDirect()
      setDirectData(response)
      console.log('问财直接请求响应:', response)

      if (response.success) {
        alert(`请求成功！状态码: ${response.statusCode}\n获取到 ${response.cookieArray?.length || 0} 个cookies`)
      } else {
        setError(response.error || '请求失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败')
      console.error('直接请求问财失败:', err)
    } finally {
      setLoading(false)
    }
  }

  // 获取问财cookies的函数
  const getIwencaiCookies = async () => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await window.electronAPI.getIwencaiCookies()
      setCookieData(response)
      console.log('问财Cookies:', response)

      if (response.success) {
        alert(`成功获取到 ${response.count} 个cookies！\nCookie字符串已复制到控制台`)
        console.log('Cookie字符串:', response.cookieString)
      } else {
        setError(response.error || '获取cookies失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取cookies失败')
      console.error('获取问财cookies失败:', err)
    } finally {
      setLoading(false)
    }
  }

  // 使用cookie调用问财API
  const fetchIwencaiApi = async () => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    // 检查是否有可用的cookies
    if (!directData?.cookies) {
      setError('请先获取问财数据以获得cookies')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await window.electronAPI.fetchIwencaiApi(directData.cookies)
      setApiData(response)
      console.log('问财API响应:', response)

      if (response.success) {
        console.log('API数据:', response.jsonData)
        alert(`API调用成功！状态码: ${response.statusCode}\n数据已输出到控制台`)
      } else {
        setError(response.error || 'API调用失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'API调用失败')
      console.error('调用问财API失败:', err)
    } finally {
      setLoading(false)
    }
  }

  // 在问财窗口中执行代码
  const executeInIwencai = async (code: string) => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    try {
      const response = await window.electronAPI.executeInIwencai(code)
      setExecuteResult(response)
      console.log('执行结果:', response)

      if (response.success) {
        alert('代码执行成功，结果已输出到控制台')
      } else {
        setError(response.error || '代码执行失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '代码执行失败')
      console.error('执行代码失败:', err)
    }
  }

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>Vite + React + Electron</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
      </div>

      <div className="iwencai-section">
        <h2>问财Cookie获取工具</h2>
        <div className="instructions">
          <p>新方案 - 无需打开浏览器窗口：</p>
          <ol>
            <li>点击"直接获取问财数据"按钮</li>
            <li>系统会自动访问问财网站并获取cookies</li>
            <li>查看获取到的cookie信息</li>
          </ol>
        </div>

        <div className="button-group">
          <button
            onClick={fetchIwencaiDirect}
            disabled={loading}
            className="iwencai-button"
          >
            {loading ? '获取中...' : '1. 获取问财数据'}
          </button>
          <button
            onClick={fetchIwencaiApi}
            disabled={loading || !directData?.cookies}
            className="api-button"
          >
            {loading ? '调用中...' : '2. 调用问财API'}
          </button>
          <button
            onClick={getIwencaiCookies}
            disabled={loading}
            className="cookie-button"
          >
            {loading ? '获取中...' : '获取Session Cookies'}
          </button>
          <button
            onClick={() => executeInIwencai('document.title')}
            className="execute-button"
          >
            测试执行代码
          </button>
        </div>

        {error && (
          <div className="error-message">
            错误: {error}
          </div>
        )}

        {directData && directData.success && (
          <div className="data-display">
            <h3>直接请求结果:</h3>
            <p><strong>状态码:</strong> {directData.statusCode}</p>
            <p><strong>获取到的Cookie数量:</strong> {directData.cookieArray?.length || 0}</p>
            <p><strong>Cookie字符串:</strong></p>
            <pre className="cookies-display">{directData.cookies || '无cookies'}</pre>
            <details>
              <summary>查看请求步骤</summary>
              <pre className="headers-display">{JSON.stringify(directData.steps, null, 2)}</pre>
            </details>
            <details>
              <summary>查看响应头</summary>
              <pre className="headers-display">{JSON.stringify(directData.headers, null, 2)}</pre>
            </details>
            <details>
              <summary>查看响应数据 (前2000字符)</summary>
              <pre className="data-preview">{directData.data}</pre>
            </details>
          </div>
        )}

        {apiData && apiData.success && (
          <div className="data-display api-result">
            <h3>问财API调用结果:</h3>
            <p><strong>状态码:</strong> {apiData.statusCode}</p>
            <p><strong>请求URL:</strong> {apiData.url}</p>
            {apiData.jsonData && (
              <div>
                <h4>解析后的JSON数据:</h4>
                <pre className="json-display">{JSON.stringify(apiData.jsonData, null, 2)}</pre>
              </div>
            )}
            <details>
              <summary>查看原始响应数据</summary>
              <pre className="data-preview">{apiData.rawData}</pre>
            </details>
            <details>
              <summary>查看响应头</summary>
              <pre className="headers-display">{JSON.stringify(apiData.headers, null, 2)}</pre>
            </details>
          </div>
        )}

        {cookieData && cookieData.success && (
          <div className="data-display">
            <h3>Session Cookie信息:</h3>
            <p><strong>获取到的Cookie数量:</strong> {cookieData.count}</p>
            <p><strong>Cookie字符串:</strong></p>
            <pre className="cookies-display">{cookieData.cookieString || '无cookies'}</pre>
            <details>
              <summary>查看详细Cookie信息</summary>
              <pre className="headers-display">{JSON.stringify(cookieData.cookies, null, 2)}</pre>
            </details>
          </div>
        )}

        {executeResult && (
          <div className="data-display">
            <h3>代码执行结果:</h3>
            <pre className="data-preview">{JSON.stringify(executeResult, null, 2)}</pre>
          </div>
        )}
      </div>
      <div className="electron-info">
        <p>
          We are using Node.js <span id="node-version"></span>,
          Chromium <span id="chrome-version"></span>,
          and Electron <span id="electron-version"></span>.
        </p>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>
    </>
  )
}

export default App
