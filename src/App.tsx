import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import type { IwencaiCookieResponse } from './types/electron'

function App() {
  const [count, setCount] = useState(0)
  const [cookieData, setCookieData] = useState<IwencaiCookieResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [executeResult, setExecuteResult] = useState<any>(null)

  // 打开问财窗口的函数
  const openIwencaiWindow = async () => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await window.electronAPI.openIwencaiWindow()
      console.log('打开问财窗口:', response)
      if (response.success) {
        alert(response.message)
      } else {
        setError('打开窗口失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '打开窗口失败')
      console.error('打开问财窗口失败:', err)
    } finally {
      setLoading(false)
    }
  }

  // 获取问财cookies的函数
  const getIwencaiCookies = async () => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await window.electronAPI.getIwencaiCookies()
      setCookieData(response)
      console.log('问财Cookies:', response)

      if (response.success) {
        alert(`成功获取到 ${response.count} 个cookies！\nCookie字符串已复制到控制台`)
        console.log('Cookie字符串:', response.cookieString)
      } else {
        setError(response.error || '获取cookies失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取cookies失败')
      console.error('获取问财cookies失败:', err)
    } finally {
      setLoading(false)
    }
  }

  // 在问财窗口中执行代码
  const executeInIwencai = async (code: string) => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    try {
      const response = await window.electronAPI.executeInIwencai(code)
      setExecuteResult(response)
      console.log('执行结果:', response)

      if (response.success) {
        alert('代码执行成功，结果已输出到控制台')
      } else {
        setError(response.error || '代码执行失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '代码执行失败')
      console.error('执行代码失败:', err)
    }
  }

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>Vite + React + Electron</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
      </div>

      <div className="iwencai-section">
        <h2>问财Cookie获取工具</h2>
        <div className="instructions">
          <p>使用步骤：</p>
          <ol>
            <li>点击"打开问财网站"按钮</li>
            <li>在新窗口中完成任何必要的验证或登录</li>
            <li>点击"获取问财Cookies"按钮获取cookies</li>
          </ol>
        </div>

        <div className="button-group">
          <button
            onClick={openIwencaiWindow}
            disabled={loading}
            className="iwencai-button"
          >
            {loading ? '打开中...' : '打开问财网站'}
          </button>
          <button
            onClick={getIwencaiCookies}
            disabled={loading}
            className="cookie-button"
          >
            {loading ? '获取中...' : '获取问财Cookies'}
          </button>
          <button
            onClick={() => executeInIwencai('document.title')}
            className="execute-button"
          >
            测试执行代码
          </button>
        </div>

        {error && (
          <div className="error-message">
            错误: {error}
          </div>
        )}

        {cookieData && cookieData.success && (
          <div className="data-display">
            <h3>Cookie信息:</h3>
            <p><strong>获取到的Cookie数量:</strong> {cookieData.count}</p>
            <p><strong>Cookie字符串:</strong></p>
            <pre className="cookies-display">{cookieData.cookieString || '无cookies'}</pre>
            <details>
              <summary>查看详细Cookie信息</summary>
              <pre className="headers-display">{JSON.stringify(cookieData.cookies, null, 2)}</pre>
            </details>
          </div>
        )}

        {executeResult && (
          <div className="data-display">
            <h3>代码执行结果:</h3>
            <pre className="data-preview">{JSON.stringify(executeResult, null, 2)}</pre>
          </div>
        )}
      </div>
      <div className="electron-info">
        <p>
          We are using Node.js <span id="node-version"></span>,
          Chromium <span id="chrome-version"></span>,
          and Electron <span id="electron-version"></span>.
        </p>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>
    </>
  )
}

export default App
