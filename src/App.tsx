import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import type { IwencaiResponse } from './types/electron'

function App() {
  const [count, setCount] = useState(0)
  const [iwencaiData, setIwencaiData] = useState<IwencaiResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 获取问财数据的函数
  const fetchIwencaiData = async () => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await window.electronAPI.fetchIwencaiData()
      setIwencaiData(response)
      console.log('问财响应:', response)
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取数据失败')
      console.error('获取问财数据失败:', err)
    } finally {
      setLoading(false)
    }
  }

  // 获取session cookies的函数
  const getSessionCookies = async () => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    try {
      const cookies = await window.electronAPI.getSessionCookies()
      console.log('Session Cookies:', cookies)
      alert(`获取到 ${cookies.length} 个cookies，请查看控制台`)
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取cookies失败')
      console.error('获取cookies失败:', err)
    }
  }

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>Vite + React + Electron</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
      </div>

      <div className="iwencai-section">
        <h2>问财数据获取</h2>
        <div className="button-group">
          <button
            onClick={fetchIwencaiData}
            disabled={loading}
            className="iwencai-button"
          >
            {loading ? '获取中...' : '获取问财数据'}
          </button>
          <button
            onClick={getSessionCookies}
            className="cookie-button"
          >
            获取Session Cookies
          </button>
        </div>

        {error && (
          <div className="error-message">
            错误: {error}
          </div>
        )}

        {iwencaiData && (
          <div className="data-display">
            <h3>响应信息:</h3>
            <p><strong>状态码:</strong> {iwencaiData.statusCode}</p>
            <p><strong>URL:</strong> {iwencaiData.url}</p>
            <p><strong>Cookies:</strong></p>
            <pre className="cookies-display">{iwencaiData.cookies || '无cookies'}</pre>
            <details>
              <summary>查看响应头</summary>
              <pre className="headers-display">{JSON.stringify(iwencaiData.headers, null, 2)}</pre>
            </details>
            <details>
              <summary>查看响应数据 (前1000字符)</summary>
              <pre className="data-preview">{iwencaiData.data.substring(0, 1000)}...</pre>
            </details>
          </div>
        )}
      </div>
      <div className="electron-info">
        <p>
          We are using Node.js <span id="node-version"></span>,
          Chromium <span id="chrome-version"></span>,
          and Electron <span id="electron-version"></span>.
        </p>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>
    </>
  )
}

export default App
