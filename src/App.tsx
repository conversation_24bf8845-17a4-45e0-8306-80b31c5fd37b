import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import type { IwencaiLoadResponse, IwencaiApiResponse } from './types/electron'

function App() {
  const [count, setCount] = useState(0)
  const [loadData, setLoadData] = useState<IwencaiLoadResponse | null>(null)
  const [apiData, setApiData] = useState<IwencaiApiResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 加载问财网站并获取cookies
  const loadIwencaiAndGetCookies = async () => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await window.electronAPI.loadIwencaiAndGetCookies()
      setLoadData(response)
      console.log('加载问财网站响应:', response)

      if (response.success) {
        alert(`成功获取到 ${response.count} 个cookies！\nCookie字符串已保存，可以调用API了`)
        console.log('Cookie字符串:', response.cookieString)
      } else {
        setError(response.error || '加载失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载失败')
      console.error('加载问财网站失败:', err)
    } finally {
      setLoading(false)
    }
  }

  // 使用获取到的cookies调用问财API
  const callIwencaiApi = async () => {
    if (!window.electronAPI) {
      setError('Electron API 不可用')
      return
    }

    if (!loadData?.cookieString) {
      setError('请先加载问财网站获取cookies')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await window.electronAPI.callIwencaiApi(loadData.cookieString)
      setApiData(response)
      console.log('问财API响应:', response)

      if (response.success) {
        console.log('API数据:', response.jsonData)
        alert(`API调用成功！状态码: ${response.statusCode}\n数据已输出到控制台`)
      } else {
        setError(response.error || 'API调用失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'API调用失败')
      console.error('调用问财API失败:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>Vite + React + Electron</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
      </div>

      <div className="iwencai-section">
        <h2>问财API调用工具</h2>
        <div className="instructions">
          <p>隐藏页面方案 - 两步完成：</p>
          <ol>
            <li>点击"加载问财网站"按钮，使用隐藏页面获取cookies</li>
            <li>点击"调用问财API"按钮获取"今日涨停"数据</li>
          </ol>
        </div>

        <div className="button-group">
          <button
            onClick={loadIwencaiAndGetCookies}
            disabled={loading}
            className="iwencai-button"
          >
            {loading ? '加载中...' : '1. 加载问财网站'}
          </button>
          <button
            onClick={callIwencaiApi}
            disabled={loading || !loadData?.cookieString}
            className="api-button"
          >
            {loading ? '调用中...' : '2. 调用问财API'}
          </button>
        </div>

        {error && (
          <div className="error-message">
            错误: {error}
          </div>
        )}

        {loadData && loadData.success && (
          <div className="data-display">
            <h3>加载问财网站结果:</h3>
            <p><strong>获取到的Cookie数量:</strong> {loadData.count}</p>
            <p><strong>访问的URL:</strong> {loadData.url}</p>
            <p><strong>Cookie字符串:</strong></p>
            <pre className="cookies-display">{loadData.cookieString || '无cookies'}</pre>
            <details>
              <summary>查看详细Cookie信息</summary>
              <pre className="headers-display">{JSON.stringify(loadData.cookies, null, 2)}</pre>
            </details>
          </div>
        )}

        {apiData && apiData.success && (
          <div className="data-display api-result">
            <h3>问财API调用结果:</h3>
            <p><strong>状态码:</strong> {apiData.statusCode}</p>
            <p><strong>请求URL:</strong> {apiData.url}</p>
            {apiData.jsonData && (
              <div>
                <h4>解析后的JSON数据:</h4>
                <pre className="json-display">{JSON.stringify(apiData.jsonData, null, 2)}</pre>
              </div>
            )}
            <details>
              <summary>查看原始响应数据</summary>
              <pre className="data-preview">{apiData.rawData}</pre>
            </details>
            <details>
              <summary>查看响应头</summary>
              <pre className="headers-display">{JSON.stringify(apiData.headers, null, 2)}</pre>
            </details>
          </div>
        )}
      </div>
      <div className="electron-info">
        <p>
          We are using Node.js <span id="node-version"></span>,
          Chromium <span id="chrome-version"></span>,
          and Electron <span id="electron-version"></span>.
        </p>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>
    </>
  )
}

export default App
