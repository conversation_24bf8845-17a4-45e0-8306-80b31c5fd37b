#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.electron-info {
  margin-top: 2em;
  padding: 1em;
  background-color: #f0f0f0;
  border-radius: 8px;
  font-size: 0.9em;
}

.electron-info p {
  margin: 0;
}

/* 问财相关样式 */
.iwencai-section {
  margin-top: 2em;
  padding: 1.5em;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.iwencai-section h2 {
  margin-top: 0;
  color: #333;
}

.button-group {
  display: flex;
  gap: 1em;
  margin-bottom: 1em;
  flex-wrap: wrap;
}

.iwencai-button, .cookie-button, .execute-button, .api-button, .api-button-renderer {
  padding: 0.75em 1.5em;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.3s;
}

.iwencai-button {
  background-color: #007bff;
  color: white;
}

.iwencai-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.iwencai-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.cookie-button {
  background-color: #28a745;
  color: white;
}

.cookie-button:hover {
  background-color: #1e7e34;
}

.execute-button {
  background-color: #ffc107;
  color: #212529;
}

.execute-button:hover {
  background-color: #e0a800;
}

.api-button {
  background-color: #17a2b8;
  color: white;
}

.api-button:hover:not(:disabled) {
  background-color: #138496;
}

.api-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.api-button-renderer {
  background-color: #28a745;
  color: white;
}

.api-button-renderer:hover:not(:disabled) {
  background-color: #218838;
}

.api-button-renderer:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.api-result {
  border-left: 4px solid #17a2b8;
}

.json-display {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1em;
  font-family: 'Courier New', monospace;
  font-size: 0.85em;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 0.5em;
}

.instructions {
  background-color: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 6px;
  padding: 1em;
  margin-bottom: 1em;
}

.instructions p {
  margin: 0 0 0.5em 0;
  font-weight: bold;
}

.instructions ol {
  margin: 0;
  padding-left: 1.5em;
}

.instructions li {
  margin-bottom: 0.25em;
}

.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 0.75em;
  border-radius: 4px;
  margin: 1em 0;
}

.data-display {
  margin-top: 1em;
  padding: 1em;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.data-display h3 {
  margin-top: 0;
  color: #333;
}

.cookies-display, .headers-display, .data-preview {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 0.75em;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

.data-preview {
  max-height: 300px;
}

details {
  margin: 0.5em 0;
}

summary {
  cursor: pointer;
  font-weight: bold;
  padding: 0.5em 0;
}

summary:hover {
  color: #007bff;
}
