export interface IwencaiOpenResponse {
  success: boolean;
  message: string;
}

export interface IwencaiCookieResponse {
  success: boolean;
  cookieString?: string;
  cookies?: any[];
  count?: number;
  error?: string;
}

export interface ExecuteResponse {
  success: boolean;
  result?: any;
  error?: string;
}

export interface ElectronAPI {
  openIwencaiWindow: () => Promise<IwencaiOpenResponse>;
  getIwencaiCookies: () => Promise<IwencaiCookieResponse>;
  executeInIwencai: (code: string) => Promise<ExecuteResponse>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
