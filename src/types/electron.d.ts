export interface IwencaiDirectResponse {
  success: boolean;
  statusCode?: number;
  headers?: any;
  cookies?: string;
  cookieArray?: string[];
  data?: string;
  url?: string;
  steps?: Array<{
    step: number;
    url: string;
    cookies: string[];
  }>;
  error?: string;
}

export interface IwencaiCookieResponse {
  success: boolean;
  cookieString?: string;
  cookies?: any[];
  count?: number;
  error?: string;
}

export interface ExecuteResponse {
  success: boolean;
  result?: any;
  error?: string;
}

export interface ElectronAPI {
  fetchIwencaiDirect: () => Promise<IwencaiDirectResponse>;
  getIwencaiCookies: () => Promise<IwencaiCookieResponse>;
  executeInIwencai: (code: string) => Promise<ExecuteResponse>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
