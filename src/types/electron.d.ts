export interface IwencaiLoadResponse {
  success: boolean;
  cookieString?: string;
  cookies?: any[];
  count?: number;
  url?: string;
  error?: string;
}

export interface IwencaiApiResponse {
  success: boolean;
  statusCode?: number;
  headers?: any;
  rawData?: string;
  jsonData?: any;
  url?: string;
  error?: string;
}

export interface ElectronAPI {
  loadIwencaiAndGetCookies: () => Promise<IwencaiLoadResponse>;
  callIwencaiApi: (cookieString: string) => Promise<IwencaiApiResponse>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
