export interface IwencaiInitResponse {
  success: boolean;
  statusCode?: number;
  cookieCount?: number;
  message?: string;
  error?: string;
}

export interface IwencaiApiResponse {
  success: boolean;
  statusCode?: number;
  headers?: any;
  rawData?: string;
  jsonData?: any;
  url?: string;
  usedCookies?: string;
  cookieCount?: number;
  error?: string;
}

export interface IwencaiCookieResponse {
  success: boolean;
  cookieString?: string;
  cookies?: any[];
  count?: number;
  error?: string;
}

export interface ExecuteResponse {
  success: boolean;
  result?: any;
  error?: string;
}

export interface ElectronAPI {
  initIwencaiCookies: () => Promise<IwencaiInitResponse>;
  fetchIwencaiApi: () => Promise<IwencaiApiResponse>;
  getIwencaiCookies: () => Promise<IwencaiCookieResponse>;
  executeInIwencai: (code: string) => Promise<ExecuteResponse>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
