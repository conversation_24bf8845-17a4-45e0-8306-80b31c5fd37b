const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 所有的Node.js API都可以在预加载过程中使用。
// 它拥有与Chrome扩展一样的沙盒。
window.addEventListener('DOMContentLoaded', () => {
  const replaceText = (selector: string, text: string) => {
    const element = document.getElementById(selector);
    if (element) {
      element.innerText = text;
    }
  };

  for (const dependency of ['chrome', 'node', 'electron']) {
    replaceText(`${dependency}-version`, process.versions[dependency] as string);
  }
});

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  fetchIwencaiDirect: () => ipcRenderer.invoke('fetch-iwencai-direct'),
  getIwencaiCookies: () => ipcRenderer.invoke('get-iwencai-cookies'),
  executeInIwencai: (code: string) => ipcRenderer.invoke('execute-in-iwencai', code)
});

// 使用CommonJS导出
module.exports = {};
