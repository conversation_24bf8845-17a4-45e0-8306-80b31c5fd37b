const { app, BrowserWindow, ipcMain, session } = require('electron');
const path = require('path');
const https = require('https');
const http = require('http');
const url = require('url');

// 禁用Electron安全警告
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

// 保持对window对象的全局引用，如果不这样做的话，当JavaScript对象被
// 垃圾回收的时候，window对象将会自动的关闭
let mainWindow = null as any;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 加载应用
  // 开发环境下，加载开发服务器地址
  mainWindow.loadURL('http://localhost:5173');
  // 打开开发者工具
  mainWindow.webContents.openDevTools();

  // 当window被关闭时，触发下面的事件
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，
    // 通常会把多个window对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时，调用这个方法
app.whenReady().then(createWindow);

// 当所有窗口都被关闭后退出应用
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当点击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (mainWindow === null) {
    createWindow();
  }
});

// 创建HTTP请求的辅助函数
function makeHttpRequest(urlString: string, options: any = {}): Promise<any> {
  return new Promise((resolve, reject) => {
    const urlObj = new url.URL(urlString);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const defaultOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
      },
      ...options
    };

    const req = client.request(defaultOptions, (res: any) => {
      let data = '';
      let cookies: string[] = [];

      // 获取响应头中的cookie
      if (res.headers['set-cookie']) {
        cookies = res.headers['set-cookie'];
      }

      res.on('data', (chunk: any) => {
        data += chunk;
      });

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          cookies: cookies,
          data: data,
          url: urlString
        });
      });
    });

    req.on('error', (error: any) => {
      reject(error);
    });

    req.end();
  });
}

// 访问问财主页获取初始cookies
ipcMain.handle('init-iwencai-cookies', async () => {
  try {
    // 访问问财主页获取初始cookies
    const homeResponse: any = await makeHttpRequest('https://www.iwencai.com/');
    console.log('问财主页响应:', homeResponse.statusCode, homeResponse.cookies);

    if (homeResponse.cookies && homeResponse.cookies.length > 0) {
      // 将cookies存储到session中
      for (const cookieStr of homeResponse.cookies) {
        try {
          const cookieParts = cookieStr.split(';')[0].split('=');
          if (cookieParts.length >= 2) {
            const name = cookieParts[0].trim();
            const value = cookieParts.slice(1).join('=').trim();

            await session.defaultSession.cookies.set({
              url: 'https://www.iwencai.com',
              name: name,
              value: value,
              domain: '.iwencai.com',
              path: '/'
            });
          }
        } catch (cookieError) {
          console.log('设置cookie失败:', cookieError);
        }
      }
    }

    return {
      success: true,
      statusCode: homeResponse.statusCode,
      cookieCount: homeResponse.cookies?.length || 0,
      message: '已初始化问财cookies到session中'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || '初始化cookies失败'
    };
  }
});

// 使用session cookies请求问财API接口
ipcMain.handle('fetch-iwencai-api', async () => {
  try {
    // 从session中获取cookies
    const sessionCookies = await session.defaultSession.cookies.get({
      domain: '.iwencai.com'
    });

    // 也获取主域名的cookies
    const mainDomainCookies = await session.defaultSession.cookies.get({
      domain: 'iwencai.com'
    });

    // 合并cookies
    const allCookies = [...sessionCookies, ...mainDomainCookies];

    // 格式化为cookie字符串
    const cookieString = allCookies
      .map((cookie: any) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    console.log('使用的cookies:', cookieString);

    const apiUrl = 'https://www.iwencai.com/customized/chart/get-robot-data';

    // 构建请求参数
    const params = new URLSearchParams({
      question: '今日涨停',
      secondary_intent: 'stock',
      perpage: '50',
      page: '1',
      source: 'Ths_iwencai_Xuangu',
      version: '2.0',
      query_area: '',
      block_list: '',
      add_info: JSON.stringify({
        urp: {
          scene: 1,
          company: 1,
          business: 1
        },
        contentType: 'json',
        searchInfo: true
      })
    });

    const requestUrl = `${apiUrl}?${params.toString()}`;

    const response: any = await makeHttpRequest(requestUrl, {
      method: 'GET',
      headers: {
        'Cookie': cookieString,
        'Referer': 'https://www.iwencai.com/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    // 尝试解析JSON数据
    let jsonData = null;
    try {
      jsonData = JSON.parse(response.data);
    } catch (parseError) {
      console.log('JSON解析失败，返回原始数据');
    }

    return {
      success: true,
      statusCode: response.statusCode,
      headers: response.headers,
      rawData: response.data,
      jsonData: jsonData,
      url: requestUrl,
      usedCookies: cookieString,
      cookieCount: allCookies.length
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'API请求失败'
    };
  }
});

// 获取问财网站的cookies
ipcMain.handle('get-iwencai-cookies', async () => {
  try {
    // 获取问财域名下的所有cookies
    const cookies = await session.defaultSession.cookies.get({
      domain: '.iwencai.com'
    });

    // 也获取主域名的cookies
    const mainDomainCookies = await session.defaultSession.cookies.get({
      domain: 'iwencai.com'
    });

    // 合并cookies
    const allCookies = [...cookies, ...mainDomainCookies];

    // 去重
    const uniqueCookies = allCookies.filter((cookie, index, self) =>
      index === self.findIndex(c => c.name === cookie.name && c.domain === cookie.domain)
    );

    // 格式化为cookie字符串
    const cookieString = uniqueCookies
      .map((cookie: any) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    return {
      success: true,
      cookieString,
      cookies: uniqueCookies,
      count: uniqueCookies.length
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取cookies失败'
    };
  }
});

// 在问财窗口中执行JavaScript代码
ipcMain.handle('execute-in-iwencai', async (_event: any, code: string) => {
  try {
    // 查找问财窗口
    const allWindows = BrowserWindow.getAllWindows();
    const iwencaiWindow = allWindows.find((win: any) =>
      win.webContents.getURL().includes('iwencai.com')
    );

    if (!iwencaiWindow) {
      return {
        success: false,
        error: '未找到问财窗口，请先打开问财网站'
      };
    }

    // 执行JavaScript代码
    const result = await iwencaiWindow.webContents.executeJavaScript(code);

    return {
      success: true,
      result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '执行代码失败'
    };
  }
});

// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用require导入。
