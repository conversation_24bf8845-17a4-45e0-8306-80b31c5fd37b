const { app, BrowserWindow, ipcMain, session } = require('electron');
const path = require('path');
const https = require('https');
const zlib = require('zlib');

// 禁用Electron安全警告
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

// 保持对window对象的全局引用，如果不这样做的话，当JavaScript对象被
// 垃圾回收的时候，window对象将会自动的关闭
let mainWindow = null as any;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 加载应用
  // 开发环境下，加载开发服务器地址
  mainWindow.loadURL('http://localhost:5173');
  // 打开开发者工具
  mainWindow.webContents.openDevTools();

  // 当window被关闭时，触发下面的事件
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，
    // 通常会把多个window对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时，调用这个方法
app.whenReady().then(createWindow);

// 当所有窗口都被关闭后退出应用
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当点击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (mainWindow === null) {
    createWindow();
  }
});

// 使用隐藏窗口加载问财网站并获取cookies
ipcMain.handle('load-iwencai-and-get-cookies', async () => {
  return new Promise((resolve, reject) => {
    // 创建隐藏的浏览器窗口
    const hiddenWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false, // 隐藏窗口
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    // 设置超时
    const timeout = setTimeout(() => {
      hiddenWindow.close();
      reject(new Error('加载超时'));
    }, 30000);

    // 监听页面加载完成
    hiddenWindow.webContents.once('did-finish-load', async () => {
      try {
        clearTimeout(timeout);

        // 等待一下确保页面完全加载
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 获取问财域名下的所有cookies
        const cookies = await session.defaultSession.cookies.get({
          domain: '.iwencai.com'
        });

        // 也获取主域名的cookies
        const mainDomainCookies = await session.defaultSession.cookies.get({
          domain: 'iwencai.com'
        });

        // 合并cookies并去重
        const allCookies = [...cookies, ...mainDomainCookies];
        const uniqueCookies = allCookies.filter((cookie, index, self) =>
          index === self.findIndex(c => c.name === cookie.name && c.domain === cookie.domain)
        );

        // 格式化为cookie字符串
        const cookieString = uniqueCookies
          .map((cookie: any) => `${cookie.name}=${cookie.value}`)
          .join('; ');

        hiddenWindow.close();

        resolve({
          success: true,
          cookieString,
          cookies: uniqueCookies,
          count: uniqueCookies.length,
          url: hiddenWindow.webContents.getURL()
        });
      } catch (error) {
        clearTimeout(timeout);
        hiddenWindow.close();
        reject(error);
      }
    });

    // 监听加载失败
    hiddenWindow.webContents.once('did-fail-load', (_event: any, errorCode: any, errorDescription: any) => {
      clearTimeout(timeout);
      hiddenWindow.close();
      reject(new Error(`加载失败: ${errorDescription} (${errorCode})`));
    });

    // 开始加载问财网站
    hiddenWindow.loadURL('https://www.iwencai.com/');
  });
});

// 使用获取到的cookies调用问财API
ipcMain.handle('call-iwencai-api', async (_event: any, cookieString: string) => {
  return new Promise((resolve, reject) => {
    const apiUrl = 'https://www.iwencai.com/customized/chart/get-robot-data';

    // 构建请求参数
    const params = new URLSearchParams({
      question: '今日涨停',
      secondary_intent: 'stock',
      perpage: '50',
      page: '1',
      source: 'Ths_iwencai_Xuangu',
      version: '2.0',
      query_area: '',
      block_list: '',
      add_info: JSON.stringify({
        urp: {
          scene: 1,
          company: 1,
          business: 1
        },
        contentType: 'json',
        searchInfo: true
      })
    });

    const requestUrl = `${apiUrl}?${params.toString()}`;

    // 使用https模块发送请求
    const urlObj = new URL(requestUrl);
    const options = {
      hostname: urlObj.hostname,
      port: 443,
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'Cookie': cookieString,
        'Referer': 'https://www.iwencai.com/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'X-Requested-With': 'XMLHttpRequest'
      }
    };

    const req = https.request(options, (res: any) => {
      const chunks: Buffer[] = [];

      res.on('data', (chunk: any) => {
        chunks.push(chunk);
      });

      res.on('end', () => {
        try {
          let buffer = Buffer.concat(chunks);
          let data = '';

          // 检查是否是gzip压缩
          const encoding = res.headers['content-encoding'];
          console.log('响应编码:', encoding);
          console.log('响应头:', res.headers);

          if (encoding === 'gzip') {
            // 解压gzip数据
            try {
              buffer = zlib.gunzipSync(buffer);
              console.log('gzip解压成功');
            } catch (gzipError) {
              console.log('gzip解压失败:', gzipError);
            }
          } else if (encoding === 'deflate') {
            // 解压deflate数据
            try {
              buffer = zlib.inflateSync(buffer);
              console.log('deflate解压成功');
            } catch (deflateError) {
              console.log('deflate解压失败:', deflateError);
            }
          } else if (encoding === 'br') {
            // 解压brotli数据
            try {
              buffer = zlib.brotliDecompressSync(buffer);
              console.log('brotli解压成功');
            } catch (brotliError) {
              console.log('brotli解压失败:', brotliError);
            }
          }

          data = buffer.toString('utf8');
          console.log('解压后数据长度:', data.length);
          console.log('解压后数据前200字符:', data.substring(0, 200));

          // 尝试解析JSON数据
          let jsonData = null;
          try {
            jsonData = JSON.parse(data);
            console.log('JSON解析成功');
          } catch (parseError) {
            console.log('JSON解析失败，返回原始数据');
          }

          resolve({
            success: true,
            statusCode: res.statusCode,
            headers: res.headers,
            rawData: data,
            jsonData: jsonData,
            url: requestUrl
          });
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', (error: any) => {
      reject({
        success: false,
        error: error.message || 'API请求失败'
      });
    });

    req.end();
  });
});

// 在渲染进程中发起API请求（这样可以在Network面板中看到）
ipcMain.handle('call-iwencai-api-in-renderer', async (_event: any, cookieString: string) => {
  return new Promise((resolve, reject) => {
    // 创建一个隐藏窗口来发起请求
    const requestWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false, // 隐藏窗口
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: false // 允许跨域请求
      }
    });

    // 设置超时
    const timeout = setTimeout(() => {
      requestWindow.close();
      reject(new Error('请求超时'));
    }, 30000);

    // 先加载问财主页设置cookies
    requestWindow.loadURL('https://www.iwencai.com/').then(() => {
      // 等待页面加载完成
      setTimeout(async () => {
        try {
          // 在渲染进程中执行API请求
          const result = await requestWindow.webContents.executeJavaScript(`
            (async function() {
              try {
                const apiUrl = 'https://www.iwencai.com/customized/chart/get-robot-data';
                const params = new URLSearchParams({
                  question: '今日涨停',
                  secondary_intent: 'stock',
                  perpage: '50',
                  page: '1',
                  source: 'Ths_iwencai_Xuangu',
                  version: '2.0',
                  query_area: '',
                  block_list: '',
                  add_info: JSON.stringify({
                    urp: {
                      scene: 1,
                      company: 1,
                      business: 1
                    },
                    contentType: 'json',
                    searchInfo: true
                  })
                });

                const requestUrl = apiUrl + '?' + params.toString();
                console.log('发起请求:', requestUrl);

                const response = await fetch(requestUrl, {
                  method: 'GET',
                  headers: {
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Referer': 'https://www.iwencai.com/',
                    'User-Agent': navigator.userAgent,
                    'X-Requested-With': 'XMLHttpRequest'
                  },
                  credentials: 'include' // 包含cookies
                });

                console.log('响应状态:', response.status);
                console.log('响应头:', Object.fromEntries(response.headers.entries()));

                const text = await response.text();
                console.log('响应数据长度:', text.length);
                console.log('响应数据前200字符:', text.substring(0, 200));

                let jsonData = null;
                try {
                  jsonData = JSON.parse(text);
                  console.log('JSON解析成功');
                } catch (e) {
                  console.log('JSON解析失败:', e.message);
                }

                return {
                  success: true,
                  statusCode: response.status,
                  headers: Object.fromEntries(response.headers.entries()),
                  rawData: text,
                  jsonData: jsonData,
                  url: requestUrl
                };
              } catch (error) {
                console.error('请求失败:', error);
                return {
                  success: false,
                  error: error.message
                };
              }
            })()
          `);

          clearTimeout(timeout);
          requestWindow.close();
          resolve(result);
        } catch (error) {
          clearTimeout(timeout);
          requestWindow.close();
          reject(error);
        }
      }, 3000); // 等待3秒确保页面完全加载
    }).catch((error: any) => {
      clearTimeout(timeout);
      requestWindow.close();
      reject(error);
    });
  });
});

// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用require导入。



// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用require导入。
