const { app, BrowserWindow, ipcMain, session } = require('electron');
const path = require('path');

// 禁用Electron安全警告
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

// 保持对window对象的全局引用，如果不这样做的话，当JavaScript对象被
// 垃圾回收的时候，window对象将会自动的关闭
let mainWindow = null as any;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 加载应用
  // 开发环境下，加载开发服务器地址
  mainWindow.loadURL('http://localhost:5173');
  // 打开开发者工具
  mainWindow.webContents.openDevTools();

  // 当window被关闭时，触发下面的事件
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，
    // 通常会把多个window对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时，调用这个方法
app.whenReady().then(createWindow);

// 当所有窗口都被关闭后退出应用
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当点击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (mainWindow === null) {
    createWindow();
  }
});

// IPC处理程序 - 使用隐藏窗口访问问财网站
ipcMain.handle('fetch-iwencai-data', async () => {
  const url = 'https://www.iwencai.com/unifiedwap/result?w=%E4%BB%8A%E6%97%A5%E6%B6%A8%E5%81%9C&querytype=stock';

  return new Promise((resolve, reject) => {
    // 创建一个隐藏的浏览器窗口来访问问财网站
    const hiddenWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false, // 隐藏窗口
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    });

    // 设置超时
    const timeout = setTimeout(() => {
      hiddenWindow.close();
      reject(new Error('请求超时'));
    }, 30000);

    // 监听页面加载完成
    hiddenWindow.webContents.once('did-finish-load', async () => {
      try {
        clearTimeout(timeout);

        // 获取页面内容
        const html = await hiddenWindow.webContents.executeJavaScript('document.documentElement.outerHTML');

        // 获取cookies
        const cookies = await session.defaultSession.cookies.get({ url: 'https://www.iwencai.com' });
        const cookieString = cookies.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; ');

        // 获取当前URL（可能有重定向）
        const currentUrl = hiddenWindow.webContents.getURL();

        hiddenWindow.close();

        resolve({
          statusCode: 200,
          headers: { 'content-type': 'text/html' },
          cookies: cookieString,
          data: html,
          url: currentUrl,
          originalUrl: url
        });
      } catch (error) {
        hiddenWindow.close();
        reject(error);
      }
    });

    // 监听加载失败
    hiddenWindow.webContents.once('did-fail-load', (_event: any, errorCode: any, errorDescription: any) => {
      clearTimeout(timeout);
      hiddenWindow.close();
      reject(new Error(`加载失败: ${errorDescription} (${errorCode})`));
    });

    // 开始加载页面
    hiddenWindow.loadURL(url);
  });
});

// 获取当前session的cookies
ipcMain.handle('get-session-cookies', async () => {
  const cookies = await session.defaultSession.cookies.get({});
  return cookies;
});

// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用require导入。
