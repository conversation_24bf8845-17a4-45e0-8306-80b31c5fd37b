const { app, BrowserWindow, ipcMain, session } = require('electron');
const path = require('path');
const https = require('https');

// 禁用Electron安全警告
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

// 保持对window对象的全局引用，如果不这样做的话，当JavaScript对象被
// 垃圾回收的时候，window对象将会自动的关闭
let mainWindow = null as any;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 加载应用
  // 开发环境下，加载开发服务器地址
  mainWindow.loadURL('http://localhost:5173');
  // 打开开发者工具
  mainWindow.webContents.openDevTools();

  // 当window被关闭时，触发下面的事件
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，
    // 通常会把多个window对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时，调用这个方法
app.whenReady().then(createWindow);

// 当所有窗口都被关闭后退出应用
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当点击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (mainWindow === null) {
    createWindow();
  }
});

// 使用隐藏窗口加载问财网站并获取cookies
ipcMain.handle('load-iwencai-and-get-cookies', async () => {
  return new Promise((resolve, reject) => {
    // 创建隐藏的浏览器窗口
    const hiddenWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false, // 隐藏窗口
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    // 设置超时
    const timeout = setTimeout(() => {
      hiddenWindow.close();
      reject(new Error('加载超时'));
    }, 30000);

    // 监听页面加载完成
    hiddenWindow.webContents.once('did-finish-load', async () => {
      try {
        clearTimeout(timeout);

        // 等待一下确保页面完全加载
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 获取问财域名下的所有cookies
        const cookies = await session.defaultSession.cookies.get({
          domain: '.iwencai.com'
        });

        // 也获取主域名的cookies
        const mainDomainCookies = await session.defaultSession.cookies.get({
          domain: 'iwencai.com'
        });

        // 合并cookies并去重
        const allCookies = [...cookies, ...mainDomainCookies];
        const uniqueCookies = allCookies.filter((cookie, index, self) =>
          index === self.findIndex(c => c.name === cookie.name && c.domain === cookie.domain)
        );

        // 格式化为cookie字符串
        const cookieString = uniqueCookies
          .map((cookie: any) => `${cookie.name}=${cookie.value}`)
          .join('; ');

        hiddenWindow.close();

        resolve({
          success: true,
          cookieString,
          cookies: uniqueCookies,
          count: uniqueCookies.length,
          url: hiddenWindow.webContents.getURL()
        });
      } catch (error) {
        clearTimeout(timeout);
        hiddenWindow.close();
        reject(error);
      }
    });

    // 监听加载失败
    hiddenWindow.webContents.once('did-fail-load', (_event: any, errorCode: any, errorDescription: any) => {
      clearTimeout(timeout);
      hiddenWindow.close();
      reject(new Error(`加载失败: ${errorDescription} (${errorCode})`));
    });

    // 开始加载问财网站
    hiddenWindow.loadURL('https://www.iwencai.com/');
  });
});

// 使用获取到的cookies调用问财API
ipcMain.handle('call-iwencai-api', async (_event: any, cookieString: string) => {
  return new Promise((resolve, reject) => {
    const apiUrl = 'https://www.iwencai.com/customized/chart/get-robot-data';

    // 构建请求参数
    const params = new URLSearchParams({
      question: '今日涨停',
      secondary_intent: 'stock',
      perpage: '50',
      page: '1',
      source: 'Ths_iwencai_Xuangu',
      version: '2.0',
      query_area: '',
      block_list: '',
      add_info: JSON.stringify({
        urp: {
          scene: 1,
          company: 1,
          business: 1
        },
        contentType: 'json',
        searchInfo: true
      })
    });

    const requestUrl = `${apiUrl}?${params.toString()}`;

    // 使用https模块发送请求
    const urlObj = new URL(requestUrl);
    const options = {
      hostname: urlObj.hostname,
      port: 443,
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'Cookie': cookieString,
        'Referer': 'https://www.iwencai.com/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'X-Requested-With': 'XMLHttpRequest'
      }
    };

    const req = https.request(options, (res: any) => {
      let data = '';

      res.on('data', (chunk: any) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          // 尝试解析JSON数据
          let jsonData = null;
          try {
            jsonData = JSON.parse(data);
          } catch (parseError) {
            console.log('JSON解析失败，返回原始数据');
          }

          resolve({
            success: true,
            statusCode: res.statusCode,
            headers: res.headers,
            rawData: data,
            jsonData: jsonData,
            url: requestUrl
          });
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', (error: any) => {
      reject({
        success: false,
        error: error.message || 'API请求失败'
      });
    });

    req.end();
  });
});

// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用require导入。



// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用require导入。
