const { app, BrowserWindow, ipcMain, session } = require('electron');
const path = require('path');

// 禁用Electron安全警告
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

// 保持对window对象的全局引用，如果不这样做的话，当JavaScript对象被
// 垃圾回收的时候，window对象将会自动的关闭
let mainWindow = null as any;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 加载应用
  // 开发环境下，加载开发服务器地址
  mainWindow.loadURL('http://localhost:5173');
  // 打开开发者工具
  mainWindow.webContents.openDevTools();

  // 当window被关闭时，触发下面的事件
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，
    // 通常会把多个window对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时，调用这个方法
app.whenReady().then(createWindow);

// 当所有窗口都被关闭后退出应用
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当点击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (mainWindow === null) {
    createWindow();
  }
});

// IPC处理程序 - 打开问财网站并获取cookies
ipcMain.handle('open-iwencai-window', async () => {
  const url = 'https://www.iwencai.com/unifiedwap/result?w=%E4%BB%8A%E6%97%A5%E6%B6%A8%E5%81%9C&querytype=stock';

  // 创建一个新的浏览器窗口来访问问财网站
  const iwencaiWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: true, // 显示窗口，让用户可以看到和交互
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false // 允许跨域请求
    }
  });

  // 加载问财网站
  await iwencaiWindow.loadURL(url);

  return {
    success: true,
    message: '问财窗口已打开，请在窗口中完成任何必要的验证，然后点击"获取Cookies"按钮'
  };
});

// 获取问财网站的cookies
ipcMain.handle('get-iwencai-cookies', async () => {
  try {
    // 获取问财域名下的所有cookies
    const cookies = await session.defaultSession.cookies.get({
      domain: '.iwencai.com'
    });

    // 也获取主域名的cookies
    const mainDomainCookies = await session.defaultSession.cookies.get({
      domain: 'iwencai.com'
    });

    // 合并cookies
    const allCookies = [...cookies, ...mainDomainCookies];

    // 去重
    const uniqueCookies = allCookies.filter((cookie, index, self) =>
      index === self.findIndex(c => c.name === cookie.name && c.domain === cookie.domain)
    );

    // 格式化为cookie字符串
    const cookieString = uniqueCookies
      .map((cookie: any) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    return {
      success: true,
      cookieString,
      cookies: uniqueCookies,
      count: uniqueCookies.length
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取cookies失败'
    };
  }
});

// 在问财窗口中执行JavaScript代码
ipcMain.handle('execute-in-iwencai', async (_event: any, code: string) => {
  try {
    // 查找问财窗口
    const allWindows = BrowserWindow.getAllWindows();
    const iwencaiWindow = allWindows.find((win: any) =>
      win.webContents.getURL().includes('iwencai.com')
    );

    if (!iwencaiWindow) {
      return {
        success: false,
        error: '未找到问财窗口，请先打开问财网站'
      };
    }

    // 执行JavaScript代码
    const result = await iwencaiWindow.webContents.executeJavaScript(code);

    return {
      success: true,
      result
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '执行代码失败'
    };
  }
});

// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用require导入。
