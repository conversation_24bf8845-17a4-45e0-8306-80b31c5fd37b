{"name": "ai-electron-app", "private": true, "version": "0.0.0", "main": "dist-electron/main.js", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "electron:dev": "concurrently \"vite\" \"tsc -p electron/tsconfig.json -w\" \"electron .\"", "electron:build": "tsc -p electron/tsconfig.json && vite build && electron-builder", "electron:preview": "npm run build && electron ."}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "electron": "^36.3.1", "electron-builder": "^26.0.12", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^4.5.2", "wait-on": "^8.0.3"}, "build": {"appId": "com.example.ai-electron-app", "productName": "AI Electron App", "directories": {"output": "release"}, "files": ["dist/**/*", "dist-electron/**/*"], "mac": {"category": "public.app-category.utilities"}, "win": {"target": ["nsis"]}, "linux": {"target": ["AppImage"], "category": "Utility"}}}